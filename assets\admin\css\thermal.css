/* 80mm Thermal Printer CSS */
@page {
  size: 80mm auto;
  margin: 1mm;
}

* {
  margin: 0;
  padding: 0;
  outline: 0;
  box-sizing: border-box;
}

body {
  font-family: "Arial", "Helvetica", sans-serif;
  font-size: 11px;
  line-height: 1.3;
  color: #000;
  width: 76mm;
  margin: 0 auto;
  padding: 2mm;
  font-weight: 500;
  -webkit-print-color-adjust: exact;
  print-color-adjust: exact;
}

/* Typography */
.strong {
  font-weight: bold;
}

.center {
  text-align: center;
}

.left {
  text-align: left;
}

.right {
  text-align: right;
}

h1,
.h1 {
  font-size: 16px;
  font-weight: bold;
  margin: 2px 0;
  text-align: center;
}

h2,
.h2 {
  font-size: 14px;
  font-weight: bold;
  margin: 2px 0;
}

h3,
.h3 {
  font-size: 12px;
  font-weight: bold;
  margin: 1px 0;
}

h4,
.h4 {
  font-size: 10px;
  font-weight: bold;
  margin: 1px 0;
}

h5,
.h5 {
  font-size: 9px;
  font-weight: bold;
  margin: 1px 0;
}

h6,
.h6 {
  font-size: 8px;
  font-weight: bold;
  margin: 1px 0;
}

/* Utilities */
.mb-1 {
  margin-bottom: 1mm;
}
.mb-2 {
  margin-bottom: 2mm;
}
.mb-3 {
  margin-bottom: 3mm;
}
.mt-1 {
  margin-top: 1mm;
}
.mt-2 {
  margin-top: 2mm;
}
.mt-3 {
  margin-top: 3mm;
}
.p-1 {
  padding: 1mm;
}
.p-2 {
  padding: 2mm;
}

/* Header */
.header {
  text-align: center;
  border-bottom: 1px dashed #000;
  padding-bottom: 2mm;
  margin-bottom: 2mm;
}

.logo {
  max-width: 40mm;
  height: auto;
  margin-bottom: 2mm;
}

.company-name {
  font-size: 12px;
  font-weight: bold;
  margin-bottom: 1mm;
}

/* Invoice Info */
.invoice-info {
  margin-bottom: 2mm;
  font-size: 10px;
  font-weight: 500;
}

.invoice-info .row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1mm;
}

/* Customer Info */
.customer-info {
  border-top: 1px dashed #000;
  border-bottom: 1px dashed #000;
  padding: 2mm 0;
  margin-bottom: 2mm;
  font-size: 10px;
  font-weight: 500;
}

/* Table Styles */
.table {
  width: 100%;
  border-collapse: collapse;
  font-size: 9px;
  margin-bottom: 2mm;
  font-weight: 500;
}

.table th {
  border-bottom: 1px solid #000;
  padding: 1mm 0;
  font-weight: bold;
  text-align: left;
}

.table td {
  padding: 1mm 0;
  border-bottom: 1px dotted #ccc;
}

.table .qty {
  text-align: center;
  width: 10mm;
}

.table .price {
  text-align: right;
  width: 15mm;
}

.table .total {
  text-align: right;
  width: 15mm;
}

.table .item {
  width: auto;
}

/* Summary */
.summary {
  border-top: 1px dashed #000;
  padding-top: 2mm;
  font-size: 10px;
  font-weight: 500;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1mm;
}

.summary-row.total {
  font-weight: bold;
  border-top: 1px solid #000;
  padding-top: 1mm;
  margin-top: 2mm;
}

/* Footer */
.footer {
  text-align: center;
  border-top: 1px dashed #000;
  padding-top: 2mm;
  margin-top: 3mm;
  font-size: 8px;
}

.thank-you {
  font-weight: bold;
  margin-bottom: 2mm;
}

/* Dashed line separator */
.separator {
  border-top: 1px dashed #000;
  margin: 2mm 0;
}

/* Text sizes */
.text-xs {
  font-size: 7px;
}
.text-sm {
  font-size: 8px;
}
.text-md {
  font-size: 9px;
}
.text-lg {
  font-size: 10px;
}
.text-xl {
  font-size: 11px;
}

/* Print specific */
@media print {
  body {
    width: 76mm;
    margin: 0;
    padding: 2mm;
  }

  .no-print {
    display: none;
  }
}

/* Flex utilities for thermal layout */
.flex {
  display: flex;
}

.justify-between {
  justify-content: space-between;
}

.justify-center {
  justify-content: center;
}

.items-center {
  align-items: center;
}

/* Thermal specific spacing */
.thermal-spacing {
  line-height: 1.1;
}

/* Cut line */
.cut-line {
  border-top: 1px dashed #000;
  margin: 3mm 0;
  text-align: center;
  font-size: 7px;
  color: #666;
}

.cut-line::after {
  content: "✂ Cut Here ✂";
  background: white;
  padding: 0 2mm;
}
