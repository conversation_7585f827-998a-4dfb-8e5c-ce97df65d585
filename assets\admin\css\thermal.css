/* 80mm Thermal Printer CSS */
@page {
  size: 80mm auto;
  margin: 0;
  padding: 0;
}

* {
  margin: 0;
  padding: 0;
  outline: 0;
  box-sizing: border-box;
}

body {
  font-family: "Arial", "Helvetica", sans-serif;
  font-size: 10px;
  line-height: 1.2;
  color: #000;
  width: 72mm;
  margin: 0 auto;
  padding: 2mm;
  font-weight: 500;
  -webkit-print-color-adjust: exact;
  print-color-adjust: exact;
  page-break-inside: avoid;
  page-break-before: avoid;
  page-break-after: avoid;
  box-sizing: border-box;
}

/* Typography */
.strong {
  font-weight: bold;
}

.center {
  text-align: center;
}

.left {
  text-align: left;
}

.right {
  text-align: right;
}

h1,
.h1 {
  font-size: 16px;
  font-weight: bold;
  margin: 2px 0;
  text-align: center;
}

h2,
.h2 {
  font-size: 14px;
  font-weight: bold;
  margin: 2px 0;
}

h3,
.h3 {
  font-size: 12px;
  font-weight: bold;
  margin: 1px 0;
}

h4,
.h4 {
  font-size: 10px;
  font-weight: bold;
  margin: 1px 0;
}

h5,
.h5 {
  font-size: 9px;
  font-weight: bold;
  margin: 1px 0;
}

h6,
.h6 {
  font-size: 8px;
  font-weight: bold;
  margin: 1px 0;
}

/* Utilities */
.mb-1 {
  margin-bottom: 1mm;
}
.mb-2 {
  margin-bottom: 2mm;
}
.mb-3 {
  margin-bottom: 3mm;
}
.mt-1 {
  margin-top: 1mm;
}
.mt-2 {
  margin-top: 2mm;
}
.mt-3 {
  margin-top: 3mm;
}
.p-1 {
  padding: 1mm;
}
.p-2 {
  padding: 2mm;
}

/* Header */
.header {
  text-align: center;
  border-bottom: 1px dashed #000;
  padding-bottom: 2mm;
  margin-bottom: 2mm;
  page-break-inside: avoid;
}

.logo {
  max-width: 40mm;
  height: auto;
  margin-bottom: 2mm;
}

.company-name {
  font-size: 12px;
  font-weight: bold;
  margin-bottom: 1mm;
}

/* Invoice Info */
.invoice-info {
  margin-bottom: 2mm;
  font-size: 10px;
  font-weight: 500;
  page-break-inside: avoid;
}

.invoice-info .row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1mm;
}

/* Customer Info */
.customer-info {
  border-top: 1px dashed #000;
  border-bottom: 1px dashed #000;
  padding: 2mm 0;
  margin-bottom: 2mm;
  font-size: 10px;
  font-weight: 500;
  page-break-inside: avoid;
}

/* Table Styles */
.table {
  width: 100%;
  border-collapse: collapse;
  font-size: 8px;
  margin-bottom: 2mm;
  font-weight: 500;
  page-break-inside: avoid;
  table-layout: fixed;
}

.table th {
  border-bottom: 1px solid #000;
  padding: 0.5mm 1mm;
  font-weight: bold;
  text-align: left;
  overflow: hidden;
}

.table td {
  padding: 0.5mm 1mm;
  border-bottom: 1px dotted #ccc;
  overflow: hidden;
  word-wrap: break-word;
}

.table .qty {
  text-align: center;
  width: 12%;
}

.table .price {
  text-align: right;
  width: 22%;
}

.table .total {
  text-align: right;
  width: 22%;
}

.table .item {
  width: 44%;
}

/* Summary */
.summary {
  border-top: 1px dashed #000;
  padding-top: 2mm;
  font-size: 10px;
  font-weight: 500;
  page-break-inside: avoid;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1mm;
}

.summary-row.total {
  font-weight: bold;
  border-top: 1px solid #000;
  padding-top: 1mm;
  margin-top: 2mm;
}

/* Footer */
.footer {
  text-align: center;
  border-top: 1px dashed #000;
  padding-top: 1mm;
  margin-top: 2mm;
  font-size: 8px;
  page-break-inside: avoid;
}

.thank-you {
  font-weight: bold;
  margin-bottom: 1mm;
}

/* Text sizes */
.text-xs {
  font-size: 7px;
}
.text-sm {
  font-size: 8px;
}
.text-md {
  font-size: 9px;
}
.text-lg {
  font-size: 10px;
}
.text-xl {
  font-size: 11px;
}

/* Print specific */
@media print {
  @page {
    size: 80mm auto;
    margin: 0 !important;
    padding: 0 !important;
  }

  body {
    width: 72mm;
    margin: 0;
    padding: 2mm;
    height: auto !important;
    overflow: visible !important;
  }

  .no-print {
    display: none;
  }

  /* Prevent ALL page breaks */
  * {
    page-break-inside: avoid !important;
    page-break-before: avoid !important;
    page-break-after: avoid !important;
    break-inside: avoid !important;
    break-before: avoid !important;
    break-after: avoid !important;
  }

  /* Force everything to stay together */
  html,
  body,
  .header,
  .content,
  .invoice-info,
  .customer-info,
  .table,
  .summary,
  .footer {
    page-break-inside: avoid !important;
    page-break-before: avoid !important;
    page-break-after: avoid !important;
    break-inside: avoid !important;
    break-before: avoid !important;
    break-after: avoid !important;
  }

  /* Ensure table rows stay together */
  tr,
  td,
  th {
    page-break-inside: avoid !important;
    break-inside: avoid !important;
  }

  /* Ensure new sections stay together */
  .sale-return-info,
  .signature-section {
    page-break-inside: avoid !important;
    break-inside: avoid !important;
  }
}

/* Flex utilities for thermal layout */
.flex {
  display: flex;
}

.justify-between {
  justify-content: space-between;
}

.justify-center {
  justify-content: center;
}

.items-center {
  align-items: center;
}

/* Thermal specific spacing */
.thermal-spacing {
  line-height: 1.1;
}

/* Sale Return Information */
.sale-return-info {
  border-top: 1px dashed #000;
  padding-top: 2mm;
  margin-top: 2mm;
  font-size: 9px;
  page-break-inside: avoid;
}

.section-title {
  font-weight: bold;
  text-align: center;
  font-size: 10px;
  margin-bottom: 2mm;
  text-decoration: underline;
}

.section-subtitle {
  font-weight: bold;
  font-size: 9px;
  margin-bottom: 1mm;
  margin-top: 2mm;
}

.return-summary {
  margin-bottom: 2mm;
}

.return-table {
  font-size: 8px;
  margin-top: 1mm;
}

.return-items {
  margin-top: 2mm;
}

/* Customer Signature Section */
.signature-section {
  margin-top: 4mm;
  margin-bottom: 2mm;
  text-align: center;
  page-break-inside: avoid;
}

.signature-line {
  border-top: 1px dotted #000;
  width: 40mm;
  margin: 0 auto 2mm auto;
  height: 8mm;
}

.signature-label {
  font-size: 8px;
  font-weight: bold;
  text-align: center;
}
