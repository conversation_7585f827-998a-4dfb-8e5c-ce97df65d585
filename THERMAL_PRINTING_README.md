# 80mm Thermal Printer Support for Sales Invoices

## Overview
This feature adds support for printing sales invoices on 80mm thermal printers. The thermal print format is optimized for small receipt printers commonly used in retail environments.

## Features Added

### 1. New Thermal Print Option
- Added "Thermal Print" option in the sales listing page
- Accessible via the "More" dropdown menu for each sale
- Generates a compact, thermal printer-friendly invoice format

### 2. Optimized Layout
- **Paper Size**: 80mm width with auto height
- **Font**: Courier New (monospace) for consistent character spacing
- **Font Size**: 10px base size with smaller sizes for details
- **Margins**: Minimal margins (1mm) to maximize print area
- **Layout**: Single column, compact design

### 3. Thermal-Specific Features
- **Company Header**: Centered company name and contact details
- **Invoice Information**: Invoice number, date, warehouse
- **Customer Details**: Name, mobile, email, address (if available)
- **Items Table**: Compact table with item name, quantity, price, total
- **Summary Section**: Subtotal, discount, grand total, received, due/change
- **Footer**: Thank you message and cut line indicator

## Files Created/Modified

### New Files:
1. `assets/admin/css/thermal.css` - Thermal printer CSS styles
2. `core/resources/views/pdf/thermal/master.blade.php` - Thermal layout template
3. `core/resources/views/pdf/thermal/sale_invoice.blade.php` - Thermal invoice template

### Modified Files:
1. `core/app/Http/Controllers/Admin/SaleController.php` - Added `downloadThermalInvoice()` method
2. `core/routes/admin.php` - Added thermal print route
3. `core/resources/views/admin/sale/index.blade.php` - Added thermal print button

## How to Use

### For Users:
1. Go to Sales → All Sales
2. Find the sale you want to print
3. Click the "More" dropdown button
4. Select "Thermal Print"
5. The thermal-formatted invoice will be generated and downloaded
6. Print the downloaded PDF on your 80mm thermal printer

### For Administrators:
1. Ensure the permission `admin.sale.invoice.thermal.pdf` is enabled for the user role
2. The permission should be automatically created when accessing the application

## Technical Details

### CSS Specifications:
- **Page Size**: 80mm width, auto height
- **Margins**: 1mm all around
- **Font Family**: Courier New (monospace)
- **Base Font Size**: 10px
- **Line Height**: 1.2

### Print Settings Recommendations:
- **Paper Size**: 80mm thermal paper
- **Margins**: Minimum (1-2mm)
- **Scale**: 100%
- **Print Background Graphics**: Enabled (for borders and lines)

### Browser Print Settings:
When printing from browser:
1. Select "More settings"
2. Set Paper size to "Custom" or "80mm"
3. Set Margins to "Minimum"
4. Enable "Background graphics"
5. Set Scale to 100%

## Customization

### Modifying the Layout:
Edit `core/resources/views/pdf/thermal/sale_invoice.blade.php` to:
- Add/remove fields
- Change text formatting
- Modify table structure

### Styling Changes:
Edit `assets/admin/css/thermal.css` to:
- Adjust font sizes
- Change spacing
- Modify colors (for thermal printers that support it)

### Company Information:
The header automatically pulls from:
- `gs('site_name')` - Company name
- `gs('site_address')` - Company address
- `gs('site_phone')` - Phone number
- `gs('site_email')` - Email address

## Troubleshooting

### Common Issues:

1. **Permission Denied**
   - Ensure the user has the `admin.sale.invoice.thermal.pdf` permission
   - Contact administrator to assign the permission

2. **Layout Issues**
   - Check printer paper width (should be 80mm)
   - Verify browser print settings
   - Ensure CSS file is loading correctly

3. **Text Too Small/Large**
   - Adjust font sizes in `thermal.css`
   - Check printer DPI settings
   - Verify browser zoom level is 100%

4. **Missing Information**
   - Check if company details are set in system settings
   - Verify customer information is complete
   - Ensure product details are properly configured

## Benefits

### For Thermal Printing:
- ✅ Optimized for 80mm thermal paper
- ✅ Clear, readable text with proper spacing
- ✅ Compact layout saves paper
- ✅ Fast printing with minimal ink usage
- ✅ Professional receipt appearance

### Compared to Regular PDF:
- ❌ Regular PDF: Designed for A4 paper (too wide)
- ❌ Regular PDF: Small text when scaled to 80mm
- ❌ Regular PDF: Wasted space and poor formatting
- ✅ Thermal PDF: Perfect fit for thermal printers
- ✅ Thermal PDF: Optimized text sizes and spacing

## Future Enhancements

Potential improvements that could be added:
- QR code generation for invoice verification
- Barcode printing for product codes
- Multiple language support for thermal templates
- Custom thermal templates per business type
- Integration with thermal printer APIs for direct printing
