@extends('pdf.thermal.master')

@section('main-content')
<!-- Invoice Header -->
<div class="invoice-info center">
    <div class="h2">{{ __(gs('site_name')) }}</div>
    <div class="strong">@lang('Invoice No.'): #{{ $sale->invoice_no }}</div>
    <div>@lang('Date'): {{ showDateTime($sale->sale_date, 'd/m/Y H:i') }}</div>
    @if($sale->warehouse)
    <div>@lang('Warehouse'): {{ $sale->warehouse->name }}</div>
    @endif
</div>

<!-- Customer Information -->
<div class="customer-info">
    <div class="strong">@lang('Bill To'):</div>
    <div>@lang('Name'): {{ $customer->name }}</div>
    @if($customer->mobile)
    <div>@lang('Mobile'): {{ $customer->mobile }}</div>
    @endif
    @if($customer->email)
    <div>@lang('Email'): {{ $customer->email }}</div>
    @endif
    @if($customer->address)
    <div>@lang('Address'): {{ $customer->address }}</div>
    @endif
</div>

<!-- Items Table -->
<table class="table">
    <thead>
        <tr>
            <th class="item">@lang('Item')</th>
            <th class="qty">@lang('Qty')</th>
            <th class="price">@lang('Price')</th>
            <th class="total">@lang('Total')</th>
        </tr>
    </thead>
    <tbody>
        @forelse($sale->saleDetails as $item)
        <tr>
            <td class="item">
                <div class="strong">{{ $item->product->name }}</div>
                @if($item->product->sku)
                <div class="text-xs">{{ $item->product->sku }}</div>
                @endif
            </td>
            <td class="qty">{{ $item->quantity }}{{ $item->product->unit ? ' ' . $item->product->unit->name : '' }}</td>
            <td class="price">{{ showAmount($item->price) }}</td>
            <td class="total">{{ showAmount($item->total) }}</td>
        </tr>
        @empty
        <tr>
            <td colspan="4" class="center">@lang('No items found')</td>
        </tr>
        @endforelse
    </tbody>
</table>

<!-- Summary -->
<div class="summary">
    <div class="summary-row">
        <span>@lang('Subtotal'):</span>
        <span>{{ showAmount($sale->total_price) }}</span>
    </div>

    @if($sale->discount_amount > 0)
    <div class="summary-row">
        <span>@lang('Discount'):</span>
        <span>-{{ showAmount($sale->discount_amount) }}</span>
    </div>
    @endif

    <div class="summary-row total">
        <span>@lang('Grand Total'):</span>
        <span>{{ showAmount($sale->receivable_amount) }}</span>
    </div>

    <div class="summary-row">
        <span>@lang('Received'):</span>
        <span>{{ showAmount($sale->received_amount) }}</span>
    </div>

    <div class="summary-row">
        <span>
            @if ($sale->due_amount >= 0)
            @lang('Due Amount'):
            @else
            @lang('Change'):
            @endif
        </span>
        <span class="strong">{{ showAmount(abs($sale->due_amount)) }}</span>
    </div>
</div>

<!-- Additional Info -->
<div class="separator"></div>
<div class="center text-sm">
    <div>@lang('Date & Time'): {{ showDateTime(now(), 'd/m/Y H:i:s') }}</div>
    @if(auth()->guard('admin')->user())
    <div>@lang('Served by'): {{ auth()->guard('admin')->user()->name }}</div>
    @endif
</div>
@endsection