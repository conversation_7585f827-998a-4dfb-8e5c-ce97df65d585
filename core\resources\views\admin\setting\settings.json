{"general_setting": {"keyword": ["general", "fundamental", "site information", "site", "website settings", "basic settings", "global settings", "site color", "timezone", "site currency", "pagination", "currency format", "site title", "base color", "secondary color", "paginate"], "title": "General Setting", "subtitle": "Configure the fundamental information of the site.", "icon": "las la-cog", "route_name": "admin.setting.general"}, "logo_favicon": {"keyword": ["branding", "identity", "logo upload", "site branding", "brand identity", "favicon", "website icon", "website favicon", "website logo"], "title": "Logo and Favicon", "subtitle": "Upload your logo and favicon here.", "icon": "las la-images", "route_name": "admin.setting.logo.icon"}, "system_configuration": {"keyword": ["basic modules", "control", "modules", "system", "configuration settings", "system control", "force ssl", "force secure password", "email control", "sms control", "verification control", "push notification control", "language control", "mobile verification", "email verification"], "title": "System Configuration", "subtitle": "Control all of the basic modules of the system.", "icon": "las la-cogs", "route_name": "admin.setting.system.configuration"}, "notification_setting": {"keyword": ["email configuration", "sms configure", "push notification configure", "email setting", "sms setting", "push notification setting", "firebase setting", "firebase control", "email template", "sms template", "push notification template", "notification template", "smtp", "sendgrid", "send grid", "mailjet", "mail jet", "php", "nexmo", "clickatell", "click a tell", "infobip", "info bip", "message bird", "sms broadcast", "twi<PERSON>", "text magic", "custom api", "template setting", "global template", "global notification"], "title": "Notification Setting", "subtitle": "Control and configure overall notification elements of the system.", "icon": "las la-bell", "route_name": "admin.setting.notification.global.email"}, "extensions": {"keyword": ["extensions", "plugins", "addons", "extension settings", "plugin settings", "addon settings", "<PERSON><PERSON>a", "custom captcha", "google captcha", "recaptcha", "re-captcha", "re captcha", "tawk", "tawk.to", "tawk to", "analytics", "google analytics", "facebook comment"], "title": "Extensions", "subtitle": "Manage extensions of the system here to extend some extra features of the system.", "icon": "las la-puzzle-piece", "route_name": "admin.extensions.index"}}